import React, { useState, useMemo } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { AlertTriangleIcon, FilterIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import * as CriteriaService from "../utils/criteria-helpers";
import type {
  CoverageGap,
  CriteriaTypeString,
  GeographyRegion,
  RoomCountRange,
} from "../types/lead-assignment";

interface CoverageGapTableProps {
  coverageGaps: CoverageGap[];
  activeCriteria: Record<string, boolean>;
  registries: {
    geographyRegions?: GeographyRegion[];
    roomCountRanges?: RoomCountRange[];
  };
}

export const CoverageGapTable: React.FC<CoverageGapTableProps> = ({
  coverageGaps,
  activeCriteria,
  registries,
}) => {
  // Get active criteria types for column headers
  const activeCriteriaTypes = Object.entries(activeCriteria)
    .filter(([_, isActive]) => isActive)
    .map(([type]) => type)
    .sort();

  // Initialize filters state
  const [filters, setFilters] = useState<Record<string, Set<string>>>({});

  // Group gaps by unique combinations
  const uniqueGaps = coverageGaps.reduce((acc, gap) => {
    const key = gap.missingCombination
      .map((c) => `${c.criteriaType}:${c.value}`)
      .sort()
      .join("|");

    if (!acc.has(key)) {
      acc.set(key, gap);
    }
    return acc;
  }, new Map<string, CoverageGap>());

  const gapsArray = Array.from(uniqueGaps.values());

  // Get all unique values for each criteria type for filter options
  const allCriteriaValues = useMemo(() => {
    const values: Record<string, Set<string>> = {};

    activeCriteriaTypes.forEach((criteriaType) => {
      values[criteriaType] = new Set();
    });

    gapsArray.forEach((gap) => {
      gap.missingCombination.forEach((criterion) => {
        if (values[criterion.criteriaType]) {
          values[criterion.criteriaType].add(criterion.value);
        }
      });
    });

    return values;
  }, [gapsArray, activeCriteriaTypes]);

  // Filter gaps based on selected filters
  const filteredGaps = useMemo(() => {
    return gapsArray.filter((gap) => {
      return Object.entries(filters).every(([criteriaType, selectedValues]) => {
        if (selectedValues.size === 0) return true;

        const gapValue = gap.missingCombination.find(
          (c) => c.criteriaType === criteriaType,
        );
        return gapValue && selectedValues.has(gapValue.value);
      });
    });
  }, [gapsArray, filters]);

  // Calculate dynamic counts based on current filters
  const dynamicCounts = useMemo(() => {
    const counts: Record<string, Map<string, number>> = {};

    activeCriteriaTypes.forEach((criteriaType) => {
      counts[criteriaType] = new Map();
    });

    // For each criteria type, count how many gaps would remain if we added each value to the filter
    activeCriteriaTypes.forEach((targetCriteriaType) => {
      allCriteriaValues[targetCriteriaType]?.forEach((value) => {
        let count = 0;

        // Count gaps that match current filters AND would match if we selected this value
        gapsArray.forEach((gap) => {
          // Check if gap matches all current filters
          const matchesCurrentFilters = Object.entries(filters).every(
            ([filterType, selectedValues]) => {
              if (selectedValues.size === 0) return true;
              if (filterType === targetCriteriaType) return true; // Skip the criteria we're calculating for

              const gapValue = gap.missingCombination.find(
                (c) => c.criteriaType === filterType,
              );
              return gapValue && selectedValues.has(gapValue.value);
            },
          );

          if (!matchesCurrentFilters) return;

          // Check if gap has this value for the target criteria
          const targetValue = gap.missingCombination.find(
            (c) => c.criteriaType === targetCriteriaType,
          );
          if (targetValue && targetValue.value === value) {
            count++;
          }
        });

        counts[targetCriteriaType].set(value, count);
      });
    });

    return counts;
  }, [gapsArray, filters, activeCriteriaTypes, allCriteriaValues]);

  const toggleFilter = (criteriaType: string, value: string) => {
    setFilters((prev) => {
      const newFilters = { ...prev };
      if (!newFilters[criteriaType]) {
        newFilters[criteriaType] = new Set();
      }

      const values = new Set(newFilters[criteriaType]);
      if (values.has(value)) {
        values.delete(value);
      } else {
        values.add(value);
      }

      newFilters[criteriaType] = values;
      return newFilters;
    });
  };

  const clearFilters = (criteriaType: string) => {
    setFilters((prev) => {
      const newFilters = { ...prev };
      newFilters[criteriaType] = new Set();
      return newFilters;
    });
  };

  return (
    <div>
      <div className="mb-4 text-sm text-muted-foreground">
        <p>
          Click the filter icons in the column headers to filter coverage gaps
          by criteria.
        </p>
      </div>

      {/* Results summary and clear all */}
      <div className="mb-2 flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {filteredGaps.length} of {gapsArray.length} coverage gaps
        </div>
        {Object.values(filters).some((f) => f.size > 0) && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setFilters({})}
            className="text-muted-foreground"
          >
            Clear all filters
          </Button>
        )}
      </div>

      <div className="overflow-x-auto">
        <Table className="border" data-testid="coverage-gap-table">
          <TableHeader>
            <TableRow data-testid="coverage-gap-table-header">
              <TableHead className="bg-muted/50 w-16 text-center">#</TableHead>
              {activeCriteriaTypes.map((criteriaType) => {
                const activeFilterCount = filters[criteriaType]?.size || 0;

                return (
                  <TableHead
                    key={criteriaType}
                    className="bg-muted/50"
                    data-testid={`coverage-gap-table-header-${criteriaType}`}
                  >
                    <div className="flex items-center gap-1">
                      <span>
                        {CriteriaService.getCriteriaDisplayName(
                          criteriaType as CriteriaTypeString,
                        )}
                      </span>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-5 w-5 p-0 relative"
                          >
                            <FilterIcon
                              className={`h-3 w-3 ${activeFilterCount > 0 ? "text-primary" : "text-muted-foreground"}`}
                            />
                            {activeFilterCount > 0 && (
                              <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-primary text-[9px] text-primary-foreground flex items-center justify-center">
                                {activeFilterCount}
                              </span>
                            )}
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="start" className="w-56">
                          <DropdownMenuLabel className="flex items-center justify-between">
                            Filter by{" "}
                            {CriteriaService.getCriteriaDisplayName(
                              criteriaType as CriteriaTypeString,
                            )}
                            {activeFilterCount > 0 && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-auto p-0 text-xs"
                                onClick={() => clearFilters(criteriaType)}
                              >
                                Clear
                              </Button>
                            )}
                          </DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <div className="max-h-64 overflow-y-auto">
                            {Array.from(allCriteriaValues[criteriaType] || [])
                              .sort((a, b) => {
                                const displayA =
                                  CriteriaService.getCriteriaValueDisplayName(
                                    criteriaType as CriteriaTypeString,
                                    a,
                                    registries,
                                  );
                                const displayB =
                                  CriteriaService.getCriteriaValueDisplayName(
                                    criteriaType as CriteriaTypeString,
                                    b,
                                    registries,
                                  );
                                return displayA.localeCompare(displayB);
                              })
                              .map((value) => {
                                const displayValue =
                                  CriteriaService.getCriteriaValueDisplayName(
                                    criteriaType as CriteriaTypeString,
                                    value,
                                    registries,
                                  );
                                const isChecked =
                                  filters[criteriaType]?.has(value) || false;
                                const count =
                                  dynamicCounts[criteriaType]?.get(value) || 0;

                                return (
                                  <DropdownMenuItem
                                    key={value}
                                    className="flex items-center justify-between"
                                    onSelect={(e) => {
                                      e.preventDefault();
                                      toggleFilter(criteriaType, value);
                                    }}
                                  >
                                    <div className="flex items-center gap-2">
                                      <Checkbox checked={isChecked} />
                                      <span
                                        className={
                                          count === 0 && !isChecked
                                            ? "text-muted-foreground"
                                            : ""
                                        }
                                      >
                                        {displayValue}
                                      </span>
                                    </div>
                                    <Badge
                                      variant={
                                        count === 0 && !isChecked
                                          ? "outline"
                                          : "secondary"
                                      }
                                      className={`ml-2 ${count === 0 && !isChecked ? "opacity-50" : ""}`}
                                    >
                                      {count}
                                    </Badge>
                                  </DropdownMenuItem>
                                );
                              })}
                          </div>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableHead>
                );
              })}
            </TableRow>
          </TableHeader>
          <TableBody data-testid="coverage-gap-table-body">
            {filteredGaps.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={activeCriteriaTypes.length + 1}
                  className="text-center text-muted-foreground py-8"
                  data-testid={gapsArray.length === 0 ? "no-validation-issues-found" : "no-gaps-match-filters"}
                >
                  {gapsArray.length === 0
                    ? "No coverage gaps found"
                    : "No gaps match the selected filters"}
                </TableCell>
              </TableRow>
            ) : (
              filteredGaps.map((gap, index) => {
                // Create a map of criteria values for this gap
                const criteriaValueMap = gap.missingCombination.reduce(
                  (map, criterion) => {
                    map[criterion.criteriaType] = criterion;
                    return map;
                  },
                  {} as Record<string, (typeof gap.missingCombination)[0]>,
                );

                // Create a data-testid that includes all criteria values
                // Use display names for test IDs to make them more readable and consistent
                const gapTestId = gap.missingCombination
                  .map((c) => {
                    const displayValue =
                      CriteriaService.getCriteriaValueDisplayName(
                        c.criteriaType as CriteriaTypeString,
                        c.value,
                        registries,
                      );
                    // Normalize the display value for use in test ID
                    const normalizedValue = displayValue
                      .toLowerCase()
                      .replace(/\s+/g, "-");
                    return `${c.criteriaType}-${normalizedValue}`;
                  })
                  .sort()
                  .join("_");

                return (
                  <TableRow
                    key={gap.id}
                    data-testid={`coverage-gap-row-${gapTestId}`}
                  >
                    <TableCell className="text-center font-medium text-muted-foreground">
                      {index + 1}
                    </TableCell>
                    {activeCriteriaTypes.map((criteriaType) => {
                      const criterion = criteriaValueMap[criteriaType];
                      if (!criterion) {
                        // This criteria type is not part of this gap
                        return (
                          <TableCell
                            key={criteriaType}
                            className="text-center text-muted-foreground"
                          >
                            -
                          </TableCell>
                        );
                      }

                      const displayValue =
                        CriteriaService.getCriteriaValueDisplayName(
                          criteriaType as CriteriaTypeString,
                          criterion.value,
                          registries,
                        );

                      return (
                        <TableCell
                          key={criteriaType}
                          data-testid={`coverage-gap-cell-${gapTestId}-${criteriaType}`}
                        >
                          <div className="flex items-center gap-2">
                            <AlertTriangleIcon className="h-4 w-4 text-warning shrink-0" />
                            <span>{displayValue}</span>
                          </div>
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>
      {filteredGaps.length > 0 && (
        <div className="mt-4 p-3 bg-warning/10 rounded-lg border border-warning/20">
          <div className="flex items-start gap-2">
            <AlertTriangleIcon className="h-4 w-4 text-warning shrink-0 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-warning">
                {filteredGaps.length} coverage{" "}
                {filteredGaps.length === 1 ? "gap" : "gaps"}{" "}
                {filteredGaps.length < gapsArray.length
                  ? "shown"
                  : "identified"}
              </p>
              <p className="text-muted-foreground mt-1">
                Leads matching these combinations will be routed to the General
                Inbox.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

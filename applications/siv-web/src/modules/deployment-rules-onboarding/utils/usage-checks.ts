import type {
  Individual,
  Team,
  CriteriaTypeString,
  CriterionValue,
} from "../types/lead-assignment";

/**
 * Generic function to check if a criteria value is being used in any assignment rules
 *
 * @param criteriaType The type of criteria to check (e.g., 'geography', 'roomCount')
 * @param valueId The ID of the value to check
 * @param individuals The array of individuals to check against
 * @param teams The array of teams to check against
 * @returns true if the value is in use, false otherwise
 */
export function isCriteriaValueInUse(
  criteriaType: CriteriaTypeString,
  valueId: string,
  individuals: Individual[],
  teams: Team[],
): boolean {
  // Check if any individual is using this value in their rules
  const individualUsingValue = individuals.some((individual) => {
    // Check each rule for the individual
    return individual.rules.some((rule) => {
      const criterion = rule.criteria[criteriaType];
      if (!criterion) return false;
      // "Any" criteria don't use specific values
      if (criterion.type === "Any") return false;
      // Check if the specific values include this valueId
      return criterion.values.includes(valueId);
    });
  });

  if (individualUsingValue) {
    return true;
  }

  // Check if any team is using this value in their rules
  const teamUsingValue = teams.some((team) => {
    // Check each rule for the team
    return team.rules.some((rule) => {
      const criterion = rule.criteria[criteriaType];
      if (!criterion) return false;
      // "Any" criteria don't use specific values
      if (criterion.type === "Any") return false;
      // Check if the specific values include this valueId
      return criterion.values.includes(valueId);
    });
  });

  return teamUsingValue;
}

/**
 * Removes invalid criteria values from all individuals and teams.
 * This function should be called whenever the available criteria values for a type change.
 *
 * @param criteriaType The type of criteria being updated (e.g., 'geography', 'roomCount')
 * @param validValues Array of valid IDs for this criteria type
 * @param individuals The array of individuals to update
 * @param teams The array of teams to update
 * @returns An object containing updated individuals and teams arrays
 */
export function cleanupInvalidCriteriaValues(
  criteriaType: CriteriaTypeString,
  validValues: string[],
  individuals: Individual[],
  teams: Team[],
): { individuals: Individual[]; teams: Team[] } {
  // Create a set of valid values for faster lookups
  const validValueSet = new Set(validValues);

  // Update individuals
  const updatedIndividuals = individuals.map((individual) => {
    let hasChanges = false;
    
    // Update rules for the individual
    const updatedRules = individual.rules.map((rule) => {
      const currentCriterion = rule.criteria[criteriaType];

      // If no criterion or it's "Any", no need to clean up
      if (!currentCriterion || currentCriterion.type === "Any") {
        return rule;
      }

      // Filter out invalid values from specific values
      const filteredValues = currentCriterion.values.filter((value) =>
        validValueSet.has(value),
      );

      // Only create a new rule if values changed
      if (filteredValues.length !== currentCriterion.values.length) {
        hasChanges = true;
        const newCriterion: CriterionValue | null =
          filteredValues.length === 0
            ? null // If no values remain, set to null
            : { type: "Specific", values: filteredValues };

        return {
          ...rule,
          criteria: {
            ...rule.criteria,
            [criteriaType]: newCriterion,
          },
        };
      }

      // Return the original rule if no changes
      return rule;
    });

    // Only return a new individual object if rules changed
    return hasChanges
      ? { ...individual, rules: updatedRules }
      : individual;
  });

  // Update teams
  const updatedTeams = teams.map((team) => {
    let hasChanges = false;
    
    // Update rules for the team
    const updatedRules = team.rules.map((rule) => {
      const currentCriterion = rule.criteria[criteriaType];

      // If no criterion or it's "Any", no need to clean up
      if (!currentCriterion || currentCriterion.type === "Any") {
        return rule;
      }

      // Filter out invalid values from specific values
      const filteredValues = currentCriterion.values.filter((value) =>
        validValueSet.has(value),
      );

      // Only create a new rule if values changed
      if (filteredValues.length !== currentCriterion.values.length) {
        hasChanges = true;
        const newCriterion: CriterionValue | null =
          filteredValues.length === 0
            ? null // If no values remain, set to null
            : { type: "Specific", values: filteredValues };

        return {
          ...rule,
          criteria: {
            ...rule.criteria,
            [criteriaType]: newCriterion,
          },
        };
      }

      // Return the original rule if no changes
      return rule;
    });

    // Only return a new team object if rules changed
    return hasChanges
      ? { ...team, rules: updatedRules }
      : team;
  });

  return { individuals: updatedIndividuals, teams: updatedTeams };
}

import { Page, Locator, expect } from '@playwright/test';

/**
 * Page object for the Coverage Gap Table component
 * Handles coverage gap table interactions and data extraction
 */
export class CoverageGapTablePageObject {
  readonly page: Page;
  readonly validationPanel: Locator;
  readonly table: Locator;

  constructor(page: Page, validationPanel: Locator) {
    this.page = page;
    this.validationPanel = validationPanel;
    this.table = validationPanel.getByTestId('coverage-gap-table');
  }

  /**
   * Check if coverage gap table is visible
   */
  async isVisible(): Promise<boolean> {
    return await this.table.isVisible().catch(() => false);
  }

  /**
   * Get coverage gap details from the validation panel
   */
  async getCoverageGapDetails() {
    await expect(this.validationPanel).toBeVisible();
    
    const summary = this.validationPanel.getByTestId('coverage-gap-summary');
    const countText = await summary.getByTestId('coverage-gap-count').textContent();
    const percentageText = await summary.getByTestId('coverage-gap-percentage').textContent();
    
    // Extract numbers from text
    const gapCount = parseInt(countText?.match(/(\d+) unassigned lead scenarios/)?.[1] || '0');
    const percentage = parseInt(percentageText?.match(/(\d+)%/)?.[1] || '0');
    
    return {
      gapCount,
      percentage,
      countText,
      percentageText
    };
  }

  /**
   * Helper to convert criteria values to normalized IDs for data-testid
   */
  private normalizeCriteriaValue(value: string): string {
    return value.toLowerCase().replace(/\s+/g, '-');
  }

  /**
   * Build coverage gap row testid from criteria
   */
  private buildCoverageGapRowTestId(criteria: Record<string, string>): string {
    const sortedEntries = Object.entries(criteria)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([type, value]) => `${type}-${this.normalizeCriteriaValue(value)}`);
    
    return `coverage-gap-row-${sortedEntries.join('_')}`;
  }

  /**
   * Verify table coverage gaps exist
   */
  async verifyTableCoverageGaps(expectedGaps: Array<Record<string, string>>) {
    await expect(this.table).toBeVisible();
    
    for (const gap of expectedGaps) {
      const testId = this.buildCoverageGapRowTestId(gap);
      const gapRow = this.table.getByTestId(testId);
      await expect(gapRow).toBeVisible();
      
      // Verify gap icons are present in the row
      const warningIcons = gapRow.locator('.lucide-triangle-alert');
      await expect(warningIcons.first()).toBeVisible();
    }
  }

  /**
   * Verify specific coverage gaps do NOT exist (are covered)
   */
  async verifyCoveredScenarios(coveredScenarios: Array<Record<string, string>>) {
    await expect(this.table).toBeVisible();
    
    for (const scenario of coveredScenarios) {
      const testId = this.buildCoverageGapRowTestId(scenario);
      const gapRow = this.table.getByTestId(testId);
      // These scenarios should NOT appear in the gap table
      await expect(gapRow).not.toBeVisible();
    }
  }

  /**
   * Get table column headers
   */
  async getColumnHeaders(): Promise<string[]> {
    const headers: string[] = [];
    const headerCells = this.table.getByTestId(/coverage-gap-table-header-/);
    const count = await headerCells.count();
    
    for (let i = 0; i < count; i++) {
      const text = await headerCells.nth(i).textContent();
      if (text) headers.push(text.trim());
    }
    
    return headers;
  }

  /**
   * Verify table has expected column headers
   */
  async verifyHeaders(expectedHeaders: string[]) {
    const actualHeaders = await this.getColumnHeaders();
    expect(actualHeaders).toEqual(expectedHeaders);
  }

  /**
   * Verify that coverage gaps table is displayed with flexible verification
   */
  async verifyTableDisplayed() {
    await expect(this.table).toBeVisible();
    
    // Verify that some coverage gap rows are present
    const gapRows = this.table.locator('[data-testid*="coverage-gap-row"]');
    const gapRowCount = await gapRows.count();
    expect(gapRowCount).toBeGreaterThan(0);
    
    console.log(`✓ Coverage gap table displays ${gapRowCount} gap rows`);
    
    // Verify warning icons are present in gap rows
    const warningIcons = this.table.locator('.lucide-triangle-alert');
    const iconCount = await warningIcons.count();
    expect(iconCount).toBeGreaterThan(0);
    
    console.log(`✓ Coverage gap table shows ${iconCount} warning icons`);
  }

  /**
   * Acknowledge coverage gaps in the validation panel
   */
  async acknowledgeCoverageGaps() {
    // Verify checkbox starts unchecked
    const acknowledgeCheckbox = this.validationPanel.getByTestId('acknowledge-gaps-checkbox');
    await expect(acknowledgeCheckbox).toHaveAttribute('data-state', 'unchecked');
    
    // Click to check the checkbox
    await acknowledgeCheckbox.click();
    await expect(acknowledgeCheckbox).toHaveAttribute('data-state', 'checked');
    
    // Click the acknowledge button
    const acknowledgeButton = this.validationPanel.getByTestId('acknowledge-gaps-button');
    await expect(acknowledgeButton).toBeEnabled();
    await expect(acknowledgeButton).toContainText('Accept Gaps & Continue');
    await acknowledgeButton.click();
    
    // Verify validation panel closes
    await expect(this.validationPanel).not.toBeVisible();
  }
}

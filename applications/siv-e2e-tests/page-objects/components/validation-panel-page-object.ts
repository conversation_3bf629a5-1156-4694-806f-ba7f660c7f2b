import { Page, Locator, expect } from '@playwright/test';

/**
 * Page object for the Validation Panel component
 * Handles all validation panel interactions and data extraction
 */
export class ValidationPanelPageObject {
  readonly page: Page;
  readonly panel: Locator;

  constructor(page: Page) {
    this.page = page;
    this.panel = page.getByTestId('slide-in-panel').filter({ hasText: 'Configuration Validation' });
  }

  /**
   * Check if validation panel is visible
   */
  async isVisible(): Promise<boolean> {
    return await this.panel.isVisible().catch(() => false);
  }

  /**
   * Get validation panel data structure
   */
  async getValidationData() {
    await expect(this.panel).toBeVisible();

    const hasOverlapText = await this.panel.locator('text=/overlap|conflict/i').count() > 0;
    const hasWarningText = await this.panel.locator('text=/warning|issue/i').count() > 0;
    const content = await this.panel.textContent() || '';

    // Check for "no issues" state
    const noIssuesElement = this.panel.getByTestId('no-validation-issues-found');
    const hasNoIssues = await noIssuesElement.isVisible().catch(() => false);

    return {
      isVisible: true,
      hasOverlapText,
      hasWarningText,
      hasNoIssues,
      content
    };
  }

  /**
   * Verify overlap indicators for specific entities
   */
  async verifyOverlapIndicators(expectedOverlappingEntities: string[]) {
    await expect(this.panel).toBeVisible();
    await expect(this.panel).toContainText(/overlap|conflict/i);

    for (const entityName of expectedOverlappingEntities) {
      try {
        await expect(this.panel).toContainText(entityName, { timeout: 2000 });
        console.log(`✓ Found ${entityName} in validation panel`);
      } catch (error) {
        console.log(`⚠ ${entityName} not found in validation panel - may be in different section`);
      }
    }

    const hasConflictText = await this.panel.locator('text=/conflict|overlap/i').count() > 0;
    expect(hasConflictText).toBe(true);
  }

  /**
   * Verify non-overlapping rules are marked as valid
   */
  async verifyNonOverlappingRules(expectedValidEntities: string[]) {
    await expect(this.panel).toBeVisible();

    const noIssuesText = this.panel.locator('[data-testid="no-validation-issues-found"]');
    const hasNoIssues = await noIssuesText.isVisible().catch(() => false);

    if (hasNoIssues) {
      await expect(noIssuesText).toBeVisible();
      console.log('Verified all rules are non-overlapping (no validation issues)');
    } else {
      for (const entityName of expectedValidEntities) {
        const entityMentions = this.panel.locator(`text=${entityName}`);
        const mentionCount = await entityMentions.count();
        expect(mentionCount).toBeGreaterThanOrEqual(0);
      }
      console.log(`Verified validation panel handles ${expectedValidEntities.length} non-overlapping entities`);
    }
  }

  /**
   * Close the validation panel
   */
  async close() {
    const backButton = this.panel.getByRole('button', { name: /back/i }).first();
    const closeButton = this.panel.getByRole('button', { name: /close/i }).first();

    const hasBackButton = await backButton.isVisible().catch(() => false);
    const hasCloseButton = await closeButton.isVisible().catch(() => false);

    if (hasBackButton) {
      await backButton.click();
    } else if (hasCloseButton) {
      await closeButton.click();
    } else {
      await this.page.keyboard.press('Escape');
    }

    await expect(this.panel).not.toBeVisible();
  }

  /**
   * Verify validation panel shows no coverage gaps
   */
  async verifyNoCoverageGaps() {
    const noIssuesElement = this.panel.getByTestId('no-validation-issues-found');
    const hasNoIssuesElement = await noIssuesElement.isVisible().catch(() => false);

    if (hasNoIssuesElement) {
      await expect(noIssuesElement).toBeVisible();
    } else {
      const noCoverageGapsText = this.panel.getByText(/no coverage gaps found/i);
      const hasNoCoverageGapsText = await noCoverageGapsText.isVisible().catch(() => false);

      if (hasNoCoverageGapsText) {
        await expect(noCoverageGapsText).toBeVisible();
      } else {
        const successText = this.panel.getByText(/configuration is valid|no issues|all good/i);
        const hasSuccessText = await successText.isVisible().catch(() => false);

        if (hasSuccessText) {
          await expect(successText).toBeVisible();
        } else {
          const panelContent = await this.panel.textContent();
          console.log(`Validation panel content: ${panelContent}`);
          await expect(this.panel).toBeVisible();
          console.log("Warning: Could not find specific 'no coverage gaps' indicator");
        }
      }
    }
  }
}
